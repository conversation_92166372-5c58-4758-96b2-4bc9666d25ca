{"name": "metamorphic-reactor", "private": true, "version": "1.0.0", "type": "module", "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "npm run dev --workspace=apps/web", "dev:api": "npm run dev --workspace=apps/api", "build": "npm run build --workspace=apps/web && npm run build --workspace=apps/api", "test": "npm run test --workspaces", "lint": "npm run lint --workspaces", "clean": "rm -rf node_modules apps/*/node_modules packages/*/node_modules"}, "devDependencies": {"@types/node": "^22.5.5", "typescript": "^5.5.3", "jest": "^29.7.0", "@types/jest": "^29.5.12", "playwright": "^1.47.0", "@playwright/test": "^1.47.0"}}