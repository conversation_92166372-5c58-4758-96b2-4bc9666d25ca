import { test, expect } from '@playwright/test';

test.describe('Metamorphic Reactor E2E', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should load the landing page', async ({ page }) => {
    await expect(page).toHaveTitle(/Metamorphic Reactor/);
    await expect(page.locator('h1')).toContainText('Metamorphic Reactor');
  });

  test('should navigate to dashboard', async ({ page }) => {
    await page.click('text=Get Started');
    await expect(page).toHaveURL(/.*dashboard/);
    await expect(page.locator('h1')).toContainText('Metamorphic Reactor');
    await expect(page.locator('text=Dashboard')).toBeVisible();
  });

  test('should display reactor prompt editor', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Check for prompt editor
    await expect(page.locator('text=Reactor Prompt')).toBeVisible();
    
    // Check for Monaco editor
    await expect(page.locator('.monaco-editor')).toBeVisible();
    
    // Check for control panel
    await expect(page.locator('text=Start')).toBeVisible();
  });

  test('should show stream panel and patch viewer', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Check for stream panel
    await expect(page.locator('text=Real-time Stream')).toBeVisible();
    await expect(page.locator('text=Stream')).toBeVisible();
    await expect(page.locator('text=Logs')).toBeVisible();
    
    // Check for patch viewer
    await expect(page.locator('text=Patch Viewer')).toBeVisible();
    await expect(page.locator('text=Diff')).toBeVisible();
    await expect(page.locator('text=Patch')).toBeVisible();
  });

  test('should handle prompt input', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Find the Monaco editor and input text
    const editor = page.locator('.monaco-editor');
    await editor.click();
    
    // Clear existing content and add new prompt
    await page.keyboard.press('Control+A');
    await page.keyboard.type('Test prompt for reactor loop');
    
    // Verify the text was entered
    await expect(page.locator('.monaco-editor')).toContainText('Test prompt');
  });

  test('should show examples panel', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Click examples button
    await page.click('text=Examples');
    
    // Check examples panel is visible
    await expect(page.locator('text=Code Examples')).toBeVisible();
    
    // Should show some example content
    await expect(page.locator('.h-full.overflow-y-auto')).toBeVisible();
  });

  test('should show help panel', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Click help button
    await page.click('text=Help');
    
    // Check help panel is visible
    await expect(page.locator('text=Help')).toBeVisible();
  });

  test('should handle navigation buttons', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Test back button
    await page.click('text=Back');
    await expect(page).toHaveURL('/');
    
    // Navigate back to dashboard
    await page.goto('/dashboard');
    
    // Test history button
    await page.click('text=History');
    await expect(page).toHaveURL(/.*history/);
    
    // Test settings button
    await page.goto('/dashboard');
    await page.click('text=Settings');
    await expect(page).toHaveURL(/.*settings/);
  });

  test('should show download button', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Download button should be visible
    await expect(page.locator('text=Download')).toBeVisible();
    
    // Click download (won't actually download in test)
    await page.click('text=Download');
  });

  test('should handle reactor loop simulation', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Enter a test prompt
    const editor = page.locator('.monaco-editor');
    await editor.click();
    await page.keyboard.press('Control+A');
    await page.keyboard.type('Optimize this function for better performance');
    
    // Start the reactor loop
    await page.click('text=Start');
    
    // Should show running state
    await expect(page.locator('text=Running')).toBeVisible();
    
    // Wait a moment for any async operations
    await page.waitForTimeout(1000);
    
    // Stop button should be available
    await expect(page.locator('text=Stop')).toBeVisible();
  });

  test('should switch between stream and logs panels', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Should start on stream panel
    await expect(page.locator('text=Real-time Stream')).toBeVisible();
    
    // Switch to logs
    await page.click('button:has-text("Logs")');
    await expect(page.locator('text=Agent Logs')).toBeVisible();
    
    // Switch back to stream
    await page.click('button:has-text("Stream")');
    await expect(page.locator('text=Real-time Stream')).toBeVisible();
  });

  test('should switch between diff and patch views', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Should start on diff view
    await expect(page.locator('text=Patch Viewer')).toBeVisible();
    
    // Switch to patch view
    await page.click('button:has-text("Patch")');
    
    // Switch back to diff
    await page.click('button:has-text("Diff")');
  });

  test('should handle responsive design', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Main elements should still be visible
    await expect(page.locator('text=Metamorphic Reactor')).toBeVisible();
    await expect(page.locator('text=Reactor Prompt')).toBeVisible();
    
    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('text=Patch Viewer')).toBeVisible();
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    await expect(page.locator('text=Real-time Stream')).toBeVisible();
  });

  test('should show proper color scheme (indigo/slate)', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Check for indigo badge
    const badge = page.locator('text=Dashboard').first();
    await expect(badge).toHaveClass(/indigo/);
    
    // Check for slate background
    const body = page.locator('body');
    await expect(body).toHaveClass(/slate/);
  });
});
