
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Key, Cpu, Github, Zap, Shield } from "lucide-react";
import { toast } from "sonner";

const Settings = () => {
  const navigate = useNavigate();
  const [plannerModel, setPlannerModel] = useState("gpt-4-turbo");
  const [criticModel, setCriticModel] = useState("claude-3-opus");
  const [loopBudget, setLoopBudget] = useState([6]);
  const [scoreThreshold, setScoreThreshold] = useState([0.95]);
  const [telemetryEnabled, setTelemetryEnabled] = useState(true);
  const [autoCommit, setAutoCommit] = useState(false);

  const handleSave = () => {
    toast.success("Settings saved successfully!");
  };

  return (
    <div className="min-h-screen bg-slate-900">
      {/* Header */}
      <div className="border-b border-slate-800 bg-slate-900/95 backdrop-blur-sm">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/dashboard')}
              className="text-slate-400 hover:text-white"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Dashboard
            </Button>
            <h1 className="text-2xl font-bold text-white">Settings</h1>
            <Badge variant="secondary" className="bg-blue-600/20 text-blue-300 border-blue-500/30">
              Configuration
            </Badge>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8 max-w-4xl">
        <div className="grid gap-8">
          {/* AI Models */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Cpu className="w-5 h-5 text-purple-400" />
                <CardTitle className="text-white">AI Models</CardTitle>
              </div>
              <CardDescription className="text-slate-300">
                Configure the AI models for your dual-agent system
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-slate-300">Planner Model</Label>
                  <Select value={plannerModel} onValueChange={setPlannerModel}>
                    <SelectTrigger className="bg-slate-900 border-slate-700 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-900 border-slate-700">
                      <SelectItem value="gpt-4-turbo">GPT-4 Turbo</SelectItem>
                      <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
                      <SelectItem value="gemini-2-pro">Gemini 2.0 Pro</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label className="text-slate-300">Critic Model</Label>
                  <Select value={criticModel} onValueChange={setCriticModel}>
                    <SelectTrigger className="bg-slate-900 border-slate-700 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-900 border-slate-700">
                      <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
                      <SelectItem value="gpt-4-turbo">GPT-4 Turbo</SelectItem>
                      <SelectItem value="gemini-2-pro">Gemini 2.0 Pro</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* API Keys */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Key className="w-5 h-5 text-yellow-400" />
                <CardTitle className="text-white">API Keys</CardTitle>
              </div>
              <CardDescription className="text-slate-300">
                Securely store your API keys for AI model access
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label className="text-slate-300">OpenAI API Key</Label>
                <Input
                  type="password"
                  placeholder="sk-..."
                  className="bg-slate-900 border-slate-700 text-white placeholder:text-slate-500"
                />
              </div>
              <div className="space-y-2">
                <Label className="text-slate-300">Anthropic API Key</Label>
                <Input
                  type="password"
                  placeholder="sk-ant-..."
                  className="bg-slate-900 border-slate-700 text-white placeholder:text-slate-500"
                />
              </div>
              <div className="space-y-2">
                <Label className="text-slate-300">Google AI API Key</Label>
                <Input
                  type="password"
                  placeholder="AIza..."
                  className="bg-slate-900 border-slate-700 text-white placeholder:text-slate-500"
                />
              </div>
            </CardContent>
          </Card>

          {/* Loop Configuration */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Zap className="w-5 h-5 text-green-400" />
                <CardTitle className="text-white">Loop Configuration</CardTitle>
              </div>
              <CardDescription className="text-slate-300">
                Fine-tune the dual-agent iteration process
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-slate-300">Maximum Iterations</Label>
                  <Badge variant="outline" className="border-slate-600 text-slate-300">
                    {loopBudget[0]}
                  </Badge>
                </div>
                <Slider
                  value={loopBudget}
                  onValueChange={setLoopBudget}
                  max={10}
                  min={1}
                  step={1}
                  className="w-full"
                />
              </div>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-slate-300">Score Threshold</Label>
                  <Badge variant="outline" className="border-slate-600 text-slate-300">
                    {scoreThreshold[0].toFixed(2)}
                  </Badge>
                </div>
                <Slider
                  value={scoreThreshold}
                  onValueChange={setScoreThreshold}
                  max={1}
                  min={0.5}
                  step={0.01}
                  className="w-full"
                />
              </div>
            </CardContent>
          </Card>

          {/* GitHub Integration */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Github className="w-5 h-5 text-green-400" />
                <CardTitle className="text-white">GitHub Integration</CardTitle>
              </div>
              <CardDescription className="text-slate-300">
                Configure automatic Git operations and PR creation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-slate-300">Auto-commit approved patches</Label>
                  <p className="text-sm text-slate-500">
                    Automatically create branch and commit when score ≥ threshold
                  </p>
                </div>
                <Switch
                  checked={autoCommit}
                  onCheckedChange={setAutoCommit}
                />
              </div>
              <Button className="w-full bg-green-600 hover:bg-green-700 text-white">
                <Github className="w-4 h-4 mr-2" />
                Connect GitHub Account
              </Button>
            </CardContent>
          </Card>

          {/* Privacy & Telemetry */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Shield className="w-5 h-5 text-blue-400" />
                <CardTitle className="text-white">Privacy & Telemetry</CardTitle>
              </div>
              <CardDescription className="text-slate-300">
                Control data collection and usage analytics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-slate-300">Enable telemetry</Label>
                  <p className="text-sm text-slate-500">
                    Help improve Metamorphic Reactor by sharing anonymous usage data
                  </p>
                </div>
                <Switch
                  checked={telemetryEnabled}
                  onCheckedChange={setTelemetryEnabled}
                />
              </div>
            </CardContent>
          </Card>

          {/* Save Button */}
          <div className="flex justify-end">
            <Button
              onClick={handleSave}
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8"
            >
              Save Settings
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
